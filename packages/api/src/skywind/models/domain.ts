import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { DynamicDomain, StaticDomain } from "../entities/domain";
import { LobbyDomainAttributes } from "../entities/lobbyDomain";
import { DOMAIN_PROVIDER, DOMAIN_STATUS, DOMAIN_TYPE } from "../utils/common";

export class UnifiedDomainModel extends Model<
    InferAttributes<UnifiedDomainModel>,
    InferCreationAttributes<UnifiedDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domainType: DOMAIN_TYPE;
    declare domain: string | null;
    declare name: string | null;
    declare environment: string | null;
    declare description: CreationOptional<string>;
    declare provider: CreationOptional<DOMAIN_PROVIDER>;
    declare status: CreationOptional<DOMAIN_STATUS>;
    declare isActive: CreationOptional<boolean>;
    declare expiryDate: CreationOptional<Date>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toDynamicInfo(): DynamicDomain {
        return {
            id: this.id,
            domain: this.domain!,
            environment: this.environment!,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    public toStaticInfo(): StaticDomain {
        return {
            id: this.id,
            domain: this.domain!,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    public toLobbyInfo(): LobbyDomainAttributes {
        return {
            id: this.id,
            name: this.name!,
            isActive: this.isActive,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    public toInfo(): DynamicDomain | StaticDomain | LobbyDomainAttributes {
        switch (this.domainType) {
            case DOMAIN_TYPE.DYNAMIC:
                return this.toDynamicInfo();
            case DOMAIN_TYPE.STATIC:
                return this.toStaticInfo();
            case DOMAIN_TYPE.LOBBY:
                return this.toLobbyInfo();
            default:
                throw new Error(`Unknown domain type: ${this.domainType}`);
        }
    }
}

export class DynamicDomainModel extends UnifiedDomainModel {
    public toInfo(): DynamicDomain {
        return this.toDynamicInfo();
    }
}

DynamicDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domainType: {
            field: "domain_type",
            type: DataTypes.ENUM(...Object.values(DOMAIN_TYPE)),
            allowNull: false,
            defaultValue: DOMAIN_TYPE.DYNAMIC
        },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false },
        name: { field: "name", type: DataTypes.STRING, allowNull: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: false },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true,
            defaultValue: DOMAIN_STATUS.ACTIVE
        },
        isActive: { field: "is_active", type: DataTypes.BOOLEAN, allowNull: true },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "unified_domains",
        sequelize: db,
        underscored: true,
        defaultScope: {
            where: {
                domainType: DOMAIN_TYPE.DYNAMIC
            }
        }
    }
);

export function getDynamicDomainModel() {
    return DynamicDomainModel;
}

export class StaticDomainModel extends UnifiedDomainModel {
    public toInfo(): StaticDomain {
        return this.toStaticInfo();
    }
}

StaticDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domainType: {
            field: "domain_type",
            type: DataTypes.ENUM(...Object.values(DOMAIN_TYPE)),
            allowNull: false,
            defaultValue: DOMAIN_TYPE.STATIC
        },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false },
        name: { field: "name", type: DataTypes.STRING, allowNull: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true,
            defaultValue: DOMAIN_STATUS.ACTIVE
        },
        isActive: { field: "is_active", type: DataTypes.BOOLEAN, allowNull: true },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "unified_domains",
        sequelize: db,
        defaultScope: {
            where: {
                domainType: DOMAIN_TYPE.STATIC
            }
        }
    }
);

export function getStaticDomainModel() {
    return StaticDomainModel;
}

export class LobbyDomainModel extends UnifiedDomainModel {
    public toInfo(): LobbyDomainAttributes {
        return this.toLobbyInfo();
    }
}

LobbyDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domainType: {
            field: "domain_type",
            type: DataTypes.ENUM(...Object.values(DOMAIN_TYPE)),
            allowNull: false,
            defaultValue: DOMAIN_TYPE.LOBBY
        },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: true },
        name: { field: "name", type: DataTypes.STRING, allowNull: false },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true
        },
        isActive: { field: "is_active", type: DataTypes.BOOLEAN, allowNull: true, defaultValue: true },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "unified_domains",
        sequelize: db,
        defaultScope: {
            where: {
                domainType: DOMAIN_TYPE.LOBBY
            }
        }
    }
);

export function getLobbyDomainModel() {
    return LobbyDomainModel;
}
