import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { DynamicDomain, StaticDomain } from "../entities/domain";
import { LobbyDomainAttributes } from "../entities/lobbyDomain";
import { DOMAIN_PROVIDER, DOMAIN_STATUS } from "../utils/common";

export class UnifiedDomainModel extends Model<
    InferAttributes<UnifiedDomainModel>,
    InferCreationAttributes<UnifiedDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string | null;
    declare name: string | null;
    declare environment: string | null;
    declare description: CreationOptional<string>;
    declare provider: CreationOptional<DOMAIN_PROVIDER>;
    declare status: CreationOptional<DOMAIN_STATUS>;
    declare isActive: CreationOptional<boolean>;
    declare expiryDate: CreationOptional<Date>;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toDynamicInfo(): DynamicDomain {
        return {
            id: this.id,
            domain: this.domain!,
            environment: this.environment!,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    public toStaticInfo(): StaticDomain {
        return {
            id: this.id,
            domain: this.domain!,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    public toLobbyInfo(): LobbyDomainAttributes {
        return {
            id: this.id,
            name: this.name!,
            isActive: this.isActive,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    }

    public toInfo(): DynamicDomain | StaticDomain | LobbyDomainAttributes {
        if (this.environment !== null) {
            return this.toDynamicInfo();
        } else if (this.domain !== null) {
            return this.toStaticInfo();
        } else {
            return this.toLobbyInfo();
        }
    }
}

export class DynamicDomainModel extends UnifiedDomainModel {
    public toInfo(): DynamicDomain {
        return this.toDynamicInfo();
    }
}

DynamicDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        name: { field: "name", type: DataTypes.STRING, allowNull: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: false },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true,
            defaultValue: DOMAIN_STATUS.ACTIVE
        },
        isActive: { field: "is_active", type: DataTypes.BOOLEAN, allowNull: true },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "dynamic_domains",
        sequelize: db,
        underscored: true,
    }
);

export function getDynamicDomainModel() {
    return DynamicDomainModel;
}

export class StaticDomainModel extends UnifiedDomainModel {
    public toInfo(): StaticDomain {
        return this.toStaticInfo();
    }
}

StaticDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        name: { field: "name", type: DataTypes.STRING, allowNull: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true,
            defaultValue: DOMAIN_STATUS.ACTIVE
        },
        isActive: { field: "is_active", type: DataTypes.BOOLEAN, allowNull: true },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "static_domains",
        sequelize: db,
    }
);

export function getStaticDomainModel() {
    return StaticDomainModel;
}

export class LobbyDomainModel extends UnifiedDomainModel {
    public toInfo(): LobbyDomainAttributes {
        return this.toLobbyInfo();
    }
}

LobbyDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: true },
        name: { field: "name", type: DataTypes.STRING, allowNull: false, unique: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: {
            field: "provider",
            type: DataTypes.ENUM(...Object.values(DOMAIN_PROVIDER)),
            allowNull: true
        },
        status: {
            field: "status",
            type: DataTypes.ENUM(...Object.values(DOMAIN_STATUS)),
            allowNull: true
        },
        isActive: { field: "is_active", type: DataTypes.BOOLEAN, allowNull: true, defaultValue: true },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "lobby_domains",
        sequelize: db,
    }
);

export function getLobbyDomainModel() {
    return LobbyDomainModel;
}
