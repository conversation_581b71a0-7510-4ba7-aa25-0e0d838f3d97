import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import { getStaticDomainModel, getLobbyDomainModel } from "./domain";
import {
    StaticDomainPoolAttributes,
    StaticDomainPoolItemAttributes,
    StaticDomainPoolLobbyItemAttributes
} from "../entities/domainPool";

const StaticDomainModel = getStaticDomainModel();
const LobbyDomainModel = getLobbyDomainModel();

export interface StaticDomainPoolDBInstance extends Model<
        InferAttributes<StaticDomainPoolDBInstance>,
        InferCreationAttributes<StaticDomainPoolDBInstance>
    >,
    StaticDomainPoolAttributes {
}

type IStaticDomainPoolModel = ModelStatic<StaticDomainPoolDBInstance>;
const StaticDomainPoolModel: IStaticDomainPoolModel =
    db.define<StaticDomainPoolDBInstance, StaticDomainPoolAttributes>(
    "StaticDomainPoolModel",
    {
        id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            field: "id"
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            field: "name"
        },
        createdAt: {
            type: DataTypes.DATE,
            field: "created_at",
        },
        updatedAt: {
            type: DataTypes.DATE,
            field: "updated_at",
        },
    },
    {
        tableName: "static_domain_pools"
    }
);

export interface StaticDomainPoolItemDBInstance extends Model<
        InferAttributes<StaticDomainPoolItemDBInstance>,
        InferCreationAttributes<StaticDomainPoolItemDBInstance>
    >,
    StaticDomainPoolItemAttributes {
}
type IStaticDomainPoolItemModel = ModelStatic<StaticDomainPoolItemDBInstance>;
const StaticDomainPoolItemModel: IStaticDomainPoolItemModel =
    db.define<StaticDomainPoolItemDBInstance, StaticDomainPoolItemAttributes>(
    "StaticDomainPoolItem",
    {
        isActive: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
            field: "is_active"
        },
        staticDomainPoolId: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: StaticDomainPoolModel,
                key: "id"
            },
            field: "static_domain_pool_id"
        },
        staticDomainId: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: StaticDomainModel,
                key: "id"
            },
            field: "static_domain_id"
        },
    },
    {
        tableName: "static_domain_pools_static_domains",
        timestamps: false
    }
);

StaticDomainPoolModel.belongsToMany(StaticDomainModel, {
    through: StaticDomainPoolItemModel,
    as: "domains",
    foreignKey: "static_domain_pool_id",
    otherKey: "static_domain_id"
});

StaticDomainModel.belongsToMany(StaticDomainPoolModel, {
    through: StaticDomainPoolItemModel,
    as: "pools",
    foreignKey: "static_domain_id",
    otherKey: "static_domain_pool_id"
});

export interface StaticDomainPoolLobbyItemDBInstance extends Model<
        InferAttributes<StaticDomainPoolLobbyItemDBInstance>,
        InferCreationAttributes<StaticDomainPoolLobbyItemDBInstance>
    >,
    StaticDomainPoolLobbyItemAttributes {
}

type IStaticDomainPoolLobbyItemModel = ModelStatic<StaticDomainPoolLobbyItemDBInstance>;
const StaticDomainPoolLobbyItemModel: IStaticDomainPoolLobbyItemModel =
    db.define<StaticDomainPoolLobbyItemDBInstance, StaticDomainPoolLobbyItemAttributes>(
    "StaticDomainPoolLobbyItem",
    {
        isActive: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
            field: "is_active"
        },
        staticDomainPoolId: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: StaticDomainPoolModel,
                key: "id"
            },
            field: "static_domain_pool_id"
        },
        lobbyDomainId: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: LobbyDomainModel,
                key: "id"
            },
            field: "lobby_domain_id"
        },
    },
    {
        tableName: "static_domain_pools_lobby_domains",
        timestamps: false
    }
);

StaticDomainPoolModel.belongsToMany(LobbyDomainModel, {
    through: StaticDomainPoolLobbyItemModel,
    as: "lobbyDomains",
    foreignKey: "static_domain_pool_id",
    otherKey: "lobby_domain_id"
});

LobbyDomainModel.belongsToMany(StaticDomainPoolModel, {
    through: StaticDomainPoolLobbyItemModel,
    as: "pools",
    foreignKey: "lobby_domain_id",
    otherKey: "static_domain_pool_id"
});

export const getStaticDomainPoolModel = () => StaticDomainPoolModel;
export const getStaticDomainPoolItemModel = () => StaticDomainPoolItemModel;
export const getStaticDomainPoolLobbyItemModel = () => StaticDomainPoolLobbyItemModel;
