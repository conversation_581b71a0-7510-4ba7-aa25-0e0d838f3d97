import logger from "../../skywind/utils/logger";
import { SyncOptions } from "sequelize";
import { get as getPaymentMethodModel } from "./payment_method";
import { get as getUserModel } from "./user";
import { get as getSiteModel } from "./site";
import { get as getAuditModel, getAuditSlaveModel, getLoginAuditModel } from "./audit";
import { get as getPlayerSessionModel } from "./playerSession";
import { get as getPlayerPasswordReset } from "./playerPasswordReset";
import { get as getPlayerModel } from "./player";
import { get as getGroupModel } from "./gamegroup";
import { get as getGameProviderModel } from "./gameprovider";
import { get as getGameGroupLimitModel } from "./gamegrouplimit";
import { get as getGameServerSettingsModel } from "./gameServerSettings";
import { getProxyModel as getProxyModel } from "./proxy";
import { get as getGameCategoryModel } from "./gamecategory";
import { getRoleModel, getUserRoleModel } from "./role";
import { get as getEntityModel } from "./entity";
import { get as getMerchantModel } from "./merchant";
import { getMerchantPlayerGameGroupModel } from "./merchantPlayerGameGroup";
import {
    getEntityLabelModel,
    getGameLabelModel as getGameLabelModel,
    getLabelModel as getLabelModel,
    getPromotionLabelModel
} from "./label";
import { getEntityGame, getGameModel } from "./game";
import { get as getAgentModel } from "./agent";
import { get as getSiteTokenModel } from "./siteToken";
import { get as getNotificationsModel } from "./notification";
import { get as getLobbyModel } from "./lobby";
import { get as getTerminalModel } from "./terminal";
import { get as getNotificationReceiverModel } from "./notificationReceiver";
import { get as getAggregateRoundModel } from "./aggrround";
import { get as getPaymentModel, getSlaveModel as getPaymentSlaveModel } from "./payment";
import { get as getCurrencyRatesModel } from "./currencyrates";
import { get as getPlayerTerminalModel } from "./playerTerminalSession";
import { getWinBetModel } from "./winBet";
import { getPromotionModel } from "./promotion";
import { get as getJurisdictionModel, getEntityJurisdictionModel } from "./jurisdiction";
import { get as getPermissionModel } from "./permission";
import { getEntityInfoModel } from "./entityInfo";
import { getAvailableSiteModel } from "./availableSites";
import { getAggrWinBetModel } from "./aggrWinBet";
import { getBrandGGRModel } from "./aggrWinBetsByBrand";
import { getBiReportModel } from "./biReports";
import { getBiSessionModel } from "./biSessions";
import { getPromotionToPlayerModel, getPromotionToPlayerUpdateModel } from "./promotionPlayer";
import { getBlockedMerchantPlayer } from "./merchantBlockedPlayer";
import { getTestMerchantPlayer } from "./merchantTestPlayer";
import { getEntityPaymentHistoryModel } from "./entityPaymentHistoryModel";
import { getDynamicDomainModel, getStaticDomainModel, getLobbyDomainModel } from "./domain";
import { getFreebetRewardModel } from "./promotionFreebetReward";
import { getBonusCoinRewardModel } from "./promotionBonusCoinReward";
import {
    getPlayerResponsibleGamingModel,
    getPlayerResponsibleGamingSettingsModel
} from "./playerResponsibleGamingSettings";
import {
    getBetWinHistoryModel as getExtBetWinHistoryModel,
    getBetWinHistoryModelSlave as getExtBetWinHistoryModelSlave,
    sync as extBetWinHistorySync
} from "@skywind-group/sw-game-provider-ext-game-history";
import { getMerchantTypeModel } from "./merchantType";
import { getSchemaDefinitionModel } from "./schemaDefinition";
import { getSchemaConfigurationDBModel } from "./schemaConfiguration";
import { getGameLimitsConfigurationModel } from "./gameLimitsConfiguration";
import { getSegmentModel } from "./segment";
import { getCurrencyMultiplierModel } from "./currencyMultiplier";
import { getDeploymentGroupModel } from "./deploymentGroup";
import { getGameVersionModel } from "./gameClientVersion";
import { getFavoriteGamesModel } from "./favoriteGame";
import { getLimitTemplateDBModel } from "./limitTemplate";
import { getGameRtpHistoryModel } from "./gameRtpHistory";
import { getAuditSummaryModel } from "./auditSummary";
import { getAuditSessionModel, getLoginAuditSessionModel } from "./auditSession";
import { getStakeRangeModel } from "./stakeRange";
import { getGameGroupFilterModel } from "./gamegroupFilter";
import { getLabelGroupModel } from "./labelGroup";
import { getLimitLevelModel } from "./limitLevels";
import { getEntityLimitLevelModel } from "./entityLimitLevels";
import { getPlayerInfoModel } from "./playerInfo";
import { getBiReportDomainsModel } from "./biReportDomains";
import {
    get as getRefreshTokenModel,
} from "./refreshToken";

import {
    getStaticDomainPoolItemModel,
    getStaticDomainPoolLobbyItemModel,
    getStaticDomainPoolModel
} from "./staticDomainPool";
import { getDynamicDomainPoolItemModel, getDynamicDomainPoolModel } from "./dynamicDomainPool";

const log = logger();

export class Models {
    public static readonly UserModel = getUserModel();
    public static readonly AuditModel = getAuditModel();
    public static readonly AuditSlaveModel = getAuditSlaveModel();
    public static readonly AuditSummaryModel = getAuditSummaryModel();
    public static readonly AuditSessionModel = getAuditSessionModel();
    public static readonly LoginAuditModel = getLoginAuditModel();
    public static readonly LoginAuditSessionModel = getLoginAuditSessionModel();
    public static readonly SiteModel = getSiteModel();
    public static readonly PlayerSessionModel = getPlayerSessionModel();
    public static readonly PlayerPasswordReset = getPlayerPasswordReset();
    public static readonly PlayerModel = getPlayerModel();
    public static readonly GameGroupModel = getGroupModel();
    public static readonly GameGroupLimitModel = getGameGroupLimitModel();
    public static readonly EntityGameModel = getEntityGame();
    public static readonly LabelGroupModel = getLabelGroupModel();
    public static readonly LabelModel = getLabelModel();
    public static readonly GameLabelModel = getGameLabelModel();
    public static readonly GameModel = getGameModel();
    public static readonly AgentModel = getAgentModel();
    public static readonly SiteTokenModel = getSiteTokenModel();
    public static readonly NotificationsModel = getNotificationsModel();
    public static readonly LobbyModel = getLobbyModel();
    public static readonly PlayerTerminalModel = getPlayerTerminalModel();
    public static readonly TerminalModel = getTerminalModel();
    public static readonly NotificationReceiverModel = getNotificationReceiverModel();
    public static readonly EntityModel = getEntityModel();
    public static readonly RoleModel = getRoleModel();
    public static readonly UserRoleModel = getUserRoleModel();
    public static readonly GameProviderModel = getGameProviderModel();
    public static readonly ProxyModel = getProxyModel();
    public static readonly MerchantModel = getMerchantModel();
    public static readonly MerchantPlayerGameGroupModel = getMerchantPlayerGameGroupModel();
    public static readonly AggregateRoundModel = getAggregateRoundModel();
    public static readonly PaymentMethodModel = getPaymentMethodModel();
    public static readonly PaymentModel = getPaymentModel();
    public static readonly PaymentSlaveModel = getPaymentSlaveModel();
    public static readonly CurrencyRatesModel = getCurrencyRatesModel();
    public static readonly WinBetModel = getWinBetModel();
    public static readonly AggrWinBetModel = getAggrWinBetModel();
    public static readonly GameCategoryModel = getGameCategoryModel();
    public static readonly Promotion = getPromotionModel();
    public static readonly PromotionFreebetReward = getFreebetRewardModel();
    public static readonly PromotionBonusCoinReward = getBonusCoinRewardModel();
    public static readonly PromotionLabelModel = getPromotionLabelModel();
    public static readonly JurisdictionModel = getJurisdictionModel();
    public static readonly EntityJurisdictionModel = getEntityJurisdictionModel();
    public static readonly PermissionModel = getPermissionModel();
    public static readonly EntityInfoModel = getEntityInfoModel();
    public static readonly AvailableSiteModel = getAvailableSiteModel();
    public static readonly BrandGGRModel = getBrandGGRModel();
    public static readonly BiReportModel = getBiReportModel();
    public static readonly BiSessionModel = getBiSessionModel();
    public static readonly PromotionToPlayerModel = getPromotionToPlayerModel();
    public static readonly PromotionToPlayerUpdateModel = getPromotionToPlayerUpdateModel();
    public static readonly MerchantBlockedPlayerModel = getBlockedMerchantPlayer();
    public static readonly MerchantTestPlayerModel = getTestMerchantPlayer();
    public static readonly EntityPaymentHistoryModel = getEntityPaymentHistoryModel();
    public static readonly DynamicDomainModel = getDynamicDomainModel();
    public static readonly StaticDomainModel = getStaticDomainModel();
    public static readonly GameServerSettingsModel = getGameServerSettingsModel();
    public static readonly PlayerResponsibleGamingModel = getPlayerResponsibleGamingModel();
    public static readonly PlayerResponsibleGamingSettingsModel = getPlayerResponsibleGamingSettingsModel();
    public static readonly ExtBetWinHistoryModel = getExtBetWinHistoryModel();
    public static readonly MerchantTypeModel = getMerchantTypeModel();
    public static readonly ExtBetWinHistoryModelSlave = getExtBetWinHistoryModelSlave();
    public static readonly SchemaDefinitionModel = getSchemaDefinitionModel();
    public static readonly SchemaConfigurationModel = getSchemaConfigurationDBModel();
    public static readonly GameLimitsConfigurationModel = getGameLimitsConfigurationModel();
    public static readonly SegmentModel = getSegmentModel();
    public static readonly CurrencyMultiplierModel = getCurrencyMultiplierModel();
    public static readonly DeploymentGroupModel = getDeploymentGroupModel();
    public static readonly GameVersionModel = getGameVersionModel();
    public static readonly FavoriteGameModel = getFavoriteGamesModel();
    public static readonly LimitTemplateDBModel = getLimitTemplateDBModel();
    public static readonly GameRtpHistoryModel = getGameRtpHistoryModel();
    public static readonly StakeRangeModel = getStakeRangeModel();
    public static readonly PlayerInfoModel = getPlayerInfoModel();
    public static readonly GameGroupFilterModel = getGameGroupFilterModel();
    public static readonly EntityLabelModel = getEntityLabelModel();
    public static readonly LimitLevelModel = getLimitLevelModel();
    public static readonly EntityGameLimitLevelModel = getEntityLimitLevelModel();
    public static readonly BiReportDomainsModel = getBiReportDomainsModel();
    public static readonly RefreshTokenModel = getRefreshTokenModel();
    public static readonly LobbyDomainModel = getLobbyDomainModel();
    public static readonly StaticDomainPoolModel = getStaticDomainPoolModel();
    public static readonly StaticDomainPoolItemModel = getStaticDomainPoolItemModel();
    public static readonly StaticDomainPoolLobbyItemModel = getStaticDomainPoolLobbyItemModel();
    public static readonly DynamicDomainPoolModel = getDynamicDomainPoolModel();
    public static readonly DynamicDomainPoolItemModel = getDynamicDomainPoolItemModel();

    public static async sync(options: SyncOptions): Promise<void> {
        log.info("Starting init DB");

        await Models.DeploymentGroupModel.sync(options);
        await Models.DynamicDomainModel.sync(options);
        await Models.EntityModel.sync(options);
        await Models.GameProviderModel.sync(options);
        await Models.UserModel.sync(options);
        await Models.SiteModel.sync(options);
        await Models.AgentModel.sync(options);
        await Models.SiteTokenModel.sync(options);
        await Models.NotificationsModel.sync(options);
        await Models.LobbyModel.sync(options);
        await Models.NotificationReceiverModel.sync(options);
        await Models.SchemaDefinitionModel.sync(options);
        await Models.GameModel.sync(options);
        await Models.EntityGameModel.sync(options);
        await Models.RoleModel.sync(options);
        await Models.UserRoleModel.sync(options);
        await Models.GameGroupModel.sync(options);
        await Models.GameGroupLimitModel.sync(options);
        await Models.PlayerModel.sync(options);
        await Models.PlayerSessionModel.sync(options);
        await Models.PlayerPasswordReset.sync(options);
        await Models.TerminalModel.sync(options);
        await Models.PlayerTerminalModel.sync(options);
        await Models.ProxyModel.sync(options);
        await Models.MerchantModel.sync(options);
        await Models.MerchantPlayerGameGroupModel.sync(options);
        await Models.AuditSummaryModel.sync(options);
        await Models.AuditSessionModel.sync(options);
        await Models.AuditModel.sync(options);
        await Models.LabelGroupModel.sync(options);
        await Models.LabelModel.sync(options);
        await Models.GameLabelModel.sync(options);
        await Models.PaymentMethodModel.sync(options);
        await Models.CurrencyRatesModel.sync(options);
        await Models.PaymentModel.sync(options);
        await Models.GameCategoryModel.sync(options);
        await Models.Promotion.sync(options);
        await Models.PromotionFreebetReward.sync(options);
        await Models.PromotionBonusCoinReward.sync(options);
        await Models.PromotionLabelModel.sync(options);
        await Models.JurisdictionModel.sync(options);
        await Models.EntityJurisdictionModel.sync(options);
        await Models.PermissionModel.sync(options);
        await Models.EntityInfoModel.sync(options);
        await Models.AvailableSiteModel.sync(options);
        await Models.BrandGGRModel.sync(options);
        await Models.BiReportModel.sync(options);
        await Models.BiSessionModel.sync(options);
        await Models.PromotionToPlayerModel.sync(options);
        await Models.PromotionToPlayerUpdateModel.sync(options);
        await Models.MerchantBlockedPlayerModel.sync(options);
        await Models.MerchantTestPlayerModel.sync(options);
        await Models.EntityPaymentHistoryModel.sync(options);
        await Models.GameServerSettingsModel.sync(options);
        await Models.PlayerResponsibleGamingModel.sync(options);
        await Models.PlayerResponsibleGamingSettingsModel.sync(options);
        await Models.MerchantTypeModel.sync(options);
        await Models.SchemaConfigurationModel.sync(options);
        await Models.SegmentModel.sync(options);
        await Models.GameLimitsConfigurationModel.sync(options);
        await Models.CurrencyMultiplierModel.sync(options);
        await Models.GameVersionModel.sync(options);
        await Models.FavoriteGameModel.sync(options);
        await Models.LimitTemplateDBModel.sync(options);
        await Models.GameRtpHistoryModel.sync(options);
        await Models.StakeRangeModel.sync(options);
        await Models.GameGroupFilterModel.sync(options);
        await Models.EntityLabelModel.sync(options);
        await Models.LimitLevelModel.sync(options);
        await Models.EntityGameLimitLevelModel.sync(options);
        await Models.PlayerInfoModel.sync(options);
        await Models.BiReportDomainsModel.sync(options);
        await Models.LoginAuditModel.sync(options);
        await Models.LoginAuditSessionModel.sync(options);
        await Models.RefreshTokenModel.sync(options);
        await Models.StaticDomainPoolModel.sync(options);
        await Models.StaticDomainPoolItemModel.sync(options);
        await Models.StaticDomainPoolLobbyItemModel.sync(options);

        await extBetWinHistorySync();
        log.info("Initialization of DB is finished.");
    }
}
