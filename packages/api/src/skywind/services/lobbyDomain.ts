import * as Errors from "../errors";
import { FindOptions, ForeignKeyConstraintError } from "sequelize";
import { lazy } from "@skywind-group/sw-utils";
import { LobbyDomainAttributes } from "../entities/lobbyDomain";
import { Models } from "../models/models";
import { DOMAIN_TYPE } from "../utils/common";

export class LobbyDomainService {

    constructor(private readonly model = Models.LobbyDomainModel) {
    }

    public async create({ isActive, name }: { name: string; isActive?: boolean }): Promise<LobbyDomainAttributes> {
        const item = await this.model.create({
            domainType: DOMAIN_TYPE.LOBBY,
            name,
            ...(isActive !== undefined ? { isActive } : {})
        });
        return item.toJSON();
    }

    public async update(id: number,
                        { name, isActive }: { name?: string; isActive?: boolean }): Promise<LobbyDomainAttributes> {
        const item = await this.model.findOne({ where: { id } });
        if (!item) {
            return Promise.reject(new Errors.DomainNotFoundError());
        }
        const data = {
            ...(name !== undefined ? { name } : {}),
            ...(isActive !== undefined ? { isActive } : {})
        };
        if (Object.keys(data).length > 0) {
            await item.update(data);
        }
        return item.toJSON();
    }

    public async findOne(id: number): Promise<LobbyDomainAttributes> {
        const item = await this.model.findByPk(id);
        if (!item) {
            return Promise.reject(new Errors.DomainNotFoundError());
        }
        return item.toJSON();
    }

    public async findAll(options: FindOptions<any> = {}): Promise<LobbyDomainAttributes[]> {
        const items = await this.model.findAll(options);
        return items.map(item => item.toJSON());
    }

    public async remove(id: number) {
        try {
            const count = await this.model.destroy({ where: { id } });
            if (!count) {
                return Promise.reject(new Errors.DomainNotFoundError());
            }
        } catch (err) {
            if (err instanceof ForeignKeyConstraintError) {
                return Promise.reject(new Errors.DomainInUseError());
            }
            return Promise.reject(err);
        }
    }
}

const lobbyDomainService = lazy(() => new LobbyDomainService());

export const getLobbyDomainService = (): LobbyDomainService => lobbyDomainService.get();
