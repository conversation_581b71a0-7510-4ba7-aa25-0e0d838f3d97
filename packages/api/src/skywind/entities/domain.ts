import { DOMAIN_PROVIDER, DOMAIN_STATUS } from "../utils/common";
import { LobbyDomainAttributes } from "./lobbyDomain";

export interface Domain {
    id?: number;
    domain: string;
    description?: string;
    provider?: DOMAIN_PROVIDER;
    status?: DOMAIN_STATUS;
    expiryDate?: Date;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface DynamicDomain extends Domain {
    environment: string;
}

export type StaticDomain = Domain;

export type UnifiedDomain = DynamicDomain | StaticDomain | LobbyDomainAttributes;
