import { BaseApiObject } from '../typings';

export type DomainType = 'static' | 'dynamic' | 'lobby' | 'live_streaming' | 'ehub' | 'bo';
export const DOMAIN_TYPES: {
  'static': DomainType,
  'dynamic': DomainType,
  'lobby': DomainType,
  'live_streaming': DomainType,
  'ehub': DomainType,
  'bo': DomainType
} = {
  'static': 'static',
  'dynamic': 'dynamic',
  'lobby': 'lobby',
  'live_streaming': 'live_streaming',
  'ehub': 'ehub',
  'bo': 'bo',
};

export enum DomainClassification {
  GENERAL = 'general',
  DYNAMIC = 'dynamic'
}

/**
 * Classifies domain types into General or Dynamic categories
 */
export function getDomainClassification(domainType: DomainType): DomainClassification {
  switch (domainType) {
    case 'dynamic':
      return DomainClassification.DYNAMIC;
    case 'static':
    case 'lobby':
    case 'live_streaming':
    case 'ehub':
    case 'bo':
      return DomainClassification.GENERAL;
    default:
      return DomainClassification.GENERAL;
  }
}

/**
 * Returns all domain types that belong to the General classification
 */
export function getGeneralDomainTypes(): DomainType[] {
  return ['static', 'lobby', 'live_streaming', 'ehub', 'bo'];
}

/**
 * Returns all domain types that belong to the Dynamic classification
 */
export function getDynamicDomainTypes(): DomainType[] {
  return ['dynamic'];
}

/**
 * Checks if a domain type is classified as General
 */
export function isGeneralDomainType(domainType: DomainType): boolean {
  return getDomainClassification(domainType) === DomainClassification.GENERAL;
}

/**
 * Checks if a domain type is classified as Dynamic
 */
export function isDynamicDomainType(domainType: DomainType): boolean {
  return getDomainClassification(domainType) === DomainClassification.DYNAMIC;
}

export interface DomainPoolItem {
  id: string;
  isActive?: boolean;
}

export interface DomainPoolRow {
  id: string;
  inherited?: boolean;
  name?: string;
  domains?: DomainPoolItem[];
  lobbyDomains?: DomainPoolItem[];
  createdAt: string;
  updatedAt: string;
}

export type DomainPool = DomainPoolRow & BaseApiObject;

export interface DomainRow {
  id: string;
  name?: string;
  domain: string;
  environment: string;
  isActive?: boolean;
  createdAt: string;
  updatedAt: string;
}

export type Domain = DomainRow & BaseApiObject;

export interface DomainsItemDialogData {
  domain?: Domain;
  gameServers?: string[];
  type: DomainType;
}
