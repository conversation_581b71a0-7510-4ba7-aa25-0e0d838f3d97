import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Domain, DOMAIN_TYPES, DomainsItemDialogData, DomainType } from '../../common/models/domain.model';
import { GameServerService } from '../game-server/game-server.service';
import { DomainsItemDialogComponent } from './domains-item-dialog/domains-item-dialog.component';
import { DomainsManagementService } from './domains-management.service';
import { MatDialog } from '@angular/material/dialog';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { DomainsPoolDialogComponent, DomainsPoolDialogData } from './domains-pool/dialog/domains-pool-dialog.component';
import { DomainsPoolService } from './domains-pool/domains-pool.service';

type PanelType = 'domain' | 'static-pool';

@Component({
  selector: 'domains-management',
  templateUrl: './domains-management.component.html',
  providers: [
    DomainsManagementService,
    GameServerService,
  ],
})
export class DomainsManagementComponent implements OnInit, OnDestroy {
  panelActions: PanelAction[] = [];
  selectedPanel: PanelType = 'static-pool';
  domainType: DomainType = 'static';
  gameServers: string[] = [];
  staticDomains: Domain[] = [];
  lobbyDomains: Domain[] = [];

  private readonly _destroyed$ = new Subject();

  constructor(private readonly service: DomainsManagementService,
              private readonly poolService: DomainsPoolService,
              private readonly gameServerService: GameServerService,
              private readonly dialog: MatDialog,
              private readonly notifications: SwuiNotificationsService,
              private readonly translate: TranslateService) {
  }

  ngOnInit() {
    this.setPanelActions();
    this.gameServerService.getList().pipe(takeUntil(this._destroyed$)).subscribe(items => {
      this.gameServers = items.map(({name}) => name);
    });
    this.service.getList(DOMAIN_TYPES.static).pipe(takeUntil(this._destroyed$)).subscribe(items => {
      this.staticDomains = items;
    });
    this.service.getList(DOMAIN_TYPES.lobby).pipe(takeUntil(this._destroyed$)).subscribe(items => {
      this.lobbyDomains = items;
    });
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  onTabChange(event: MatTabChangeEvent) {
    // Map tab indices to domain types for existing tabs
    const tabToDomainTypeMap = {
      1: DOMAIN_TYPES.static,
      2: DOMAIN_TYPES.lobby,
      3: DOMAIN_TYPES.dynamic
    };

    if (event.index === 0) {
      this.selectedPanel = 'static-pool';
    } else if (tabToDomainTypeMap[event.index]) {
      this.selectedPanel = 'domain';
      this.domainType = tabToDomainTypeMap[event.index];
    } else {
      // Default fallback for any additional tabs
      this.selectedPanel = 'static-pool';
    }
    this.setPanelActions();
  }

  private setPanelActions() {
    if (this.selectedPanel === 'static-pool') {
      this.panelActions = [{
        title: 'DOMAINS.addPool',
        color: 'primary',
        icon: 'add',
        actionFn: () => {
          const data: DomainsPoolDialogData = {
            domains: this.staticDomains,
            lobbyDomains: this.lobbyDomains
          };
          const dialogRef = this.dialog.open(DomainsPoolDialogComponent, {
            width: '500px',
            data,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(record => !!record),
              switchMap(record => this.poolService.create(record)),
              switchMap(pool => this.translate.get('DOMAINS.notificationPoolCreated', {name: pool.name})),
              tap(message => this.notifications.success(message)),
              takeUntil(this._destroyed$)
            )
            .subscribe(() => {
              this.poolService.isGridChanged$.next();
            });
        },
      }];
    } else {
      this.panelActions = [{
        title: 'DOMAINS.addDomain',
        color: 'primary',
        icon: 'add',
        actionFn: () => {
          const data: DomainsItemDialogData = {
            gameServers: this.gameServers,
            type: this.domainType
          };
          const dialogRef = this.dialog.open(DomainsItemDialogComponent, {
            width: '500px',
            data,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(domain => !!domain),
              switchMap((domain: Domain) => this.service.create(domain, this.domainType)),
              switchMap((domain: Domain) => this.translate.get('DOMAINS.notificationCreated', {domain: domain.domain})),
              tap(message => this.notifications.success(message)),
              takeUntil(this._destroyed$)
            )
            .subscribe(() => {
              this.service.isGridChanged$.next(this.domainType);
            });
        },
      }];
    }
  }
}
