--liquibase formatted sql

--changeset vladimir.minakov:2025-07-17-SWS-51269-consolidate-domain-tables
--comment Consolidate dynamic_domains, static_domains, and lobby_domains into unified_domains table
SET search_path = swmanagement;

-- Create the unified domains table
CREATE TABLE unified_domains (
    id SERIAL PRIMARY KEY,
    domain_type VARCHAR(20) NOT NULL CHECK (domain_type IN ('dynamic', 'static', 'lobby')),
    domain VARCHAR(255),
    name VARCHA<PERSON>(255),
    environment VARCHAR(255),
    description TEXT,
    provider VARCHAR(50),
    status VARCHAR(50) DEFAULT 'active',
    is_active BOOLEAN DEFAULT true,
    expiry_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_unified_domains_domain_type ON unified_domains(domain_type);
CREATE INDEX idx_unified_domains_domain ON unified_domains(domain) WHERE domain IS NOT NULL;
CREATE INDEX idx_unified_domains_name ON unified_domains(name) WHERE name IS NOT NULL;
CREATE INDEX idx_unified_domains_environment ON unified_domains(environment) WHERE environment IS NOT NULL;

-- Create unique constraints based on domain type
CREATE UNIQUE INDEX idx_unified_domains_dynamic_unique ON unified_domains(domain) 
    WHERE domain_type = 'dynamic' AND domain IS NOT NULL;
CREATE UNIQUE INDEX idx_unified_domains_static_unique ON unified_domains(domain) 
    WHERE domain_type = 'static' AND domain IS NOT NULL;
CREATE UNIQUE INDEX idx_unified_domains_lobby_unique ON unified_domains(name) 
    WHERE domain_type = 'lobby' AND name IS NOT NULL;

-- Migrate data from dynamic_domains
INSERT INTO unified_domains (
    domain_type, domain, environment, description, provider, status, expiry_date, created_at, updated_at
)
SELECT 
    'dynamic'::VARCHAR(20),
    domain,
    environment,
    description,
    provider,
    status,
    expiry_date,
    created_at,
    updated_at
FROM dynamic_domains;

-- Migrate data from static_domains
INSERT INTO unified_domains (
    domain_type, domain, description, provider, status, expiry_date, created_at, updated_at
)
SELECT 
    'static'::VARCHAR(20),
    domain,
    description,
    provider,
    status,
    expiry_date,
    created_at,
    updated_at
FROM static_domains;

-- Migrate data from lobby_domains
INSERT INTO unified_domains (
    domain_type, name, is_active, created_at, updated_at
)
SELECT 
    'lobby'::VARCHAR(20),
    name,
    is_active,
    created_at,
    updated_at
FROM lobby_domains;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback DROP TABLE IF EXISTS unified_domains;
--rollback RESET search_path;


--changeset vladimir.minakov:2025-07-17-SWS-51269-update-foreign-keys
--comment Update foreign key references to point to unified_domains table
SET search_path = swmanagement;

-- Update dynamic_domain_pools_domains table to reference unified_domains
-- First, we need to update the foreign key references
UPDATE dynamic_domain_pools_domains 
SET dynamic_domain_id = (
    SELECT ud.id 
    FROM unified_domains ud 
    JOIN dynamic_domains dd ON dd.domain = ud.domain 
    WHERE dd.id = dynamic_domain_pools_domains.dynamic_domain_id 
    AND ud.domain_type = 'dynamic'
);

-- Update static_domain_pools_domains table to reference unified_domains
UPDATE static_domain_pools_domains 
SET static_domain_id = (
    SELECT ud.id 
    FROM unified_domains ud 
    JOIN static_domains sd ON sd.domain = ud.domain 
    WHERE sd.id = static_domain_pools_domains.static_domain_id 
    AND ud.domain_type = 'static'
);

-- Update static_domain_pools_lobby_domains table to reference unified_domains
UPDATE static_domain_pools_lobby_domains 
SET lobby_domain_id = (
    SELECT ud.id 
    FROM unified_domains ud 
    JOIN lobby_domains ld ON ld.name = ud.name 
    WHERE ld.id = static_domain_pools_lobby_domains.lobby_domain_id 
    AND ud.domain_type = 'lobby'
);

-- Update entities table dynamic_domain_id references
UPDATE entities 
SET dynamic_domain_id = (
    SELECT ud.id 
    FROM unified_domains ud 
    JOIN dynamic_domains dd ON dd.id = entities.dynamic_domain_id 
    WHERE ud.domain = dd.domain 
    AND ud.domain_type = 'dynamic'
)
WHERE dynamic_domain_id IS NOT NULL;

-- Update entities table prev_dynamic_domain_id references
UPDATE entities 
SET prev_dynamic_domain_id = (
    SELECT ud.id 
    FROM unified_domains ud 
    JOIN dynamic_domains dd ON dd.id = entities.prev_dynamic_domain_id 
    WHERE ud.domain = dd.domain 
    AND ud.domain_type = 'dynamic'
)
WHERE prev_dynamic_domain_id IS NOT NULL;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback -- Rollback would require restoring original foreign key values, which is complex
--rollback -- This should be handled carefully in production
--rollback RESET search_path;


--changeset vladimir.minakov:2025-07-17-SWS-51269-drop-old-tables
--comment Drop the original domain tables after successful migration
SET search_path = swmanagement;

-- Drop the original tables
DROP TABLE IF EXISTS dynamic_domains CASCADE;
DROP TABLE IF EXISTS static_domains CASCADE;
DROP TABLE IF EXISTS lobby_domains CASCADE;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback -- Recreating the original tables would require complex data restoration
--rollback -- This migration should be tested thoroughly before production deployment
--rollback RESET search_path;
